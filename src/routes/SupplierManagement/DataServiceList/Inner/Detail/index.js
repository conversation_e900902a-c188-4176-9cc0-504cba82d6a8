import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment, useMemo } from 'react';
import { Form, Icon, message, PageContainer, Anchor, Badge, Tag, Tabs, Title, Descriptions, Table, Tooltip, Ellipsis, Button } from 'tntd';
import { connect } from 'dva';
import { parse } from 'query-string';

import classNames from 'classnames';
import FlowLimitConfigTable from '../AddModify/SyncService/Components/FlowLimitConfigTable';

import { dataServiceListAPI, systemFieldsAPI, supplierListAPI, captainAPI, contractListAPI } from '@/services';
import etlAPI from '../../../Etl/services';
import MockConfig from './MockConfig';

import './index.less';
const { TabPane } = Tabs;
const { Link } = Anchor;
const Detail = connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))((props) => {
    const { location, globalStore, dispatch, history } = props;
    const { allMap } = globalStore;
    const { chargeMethodList = [] } = allMap;

    const { modalType, dataSourceType, uuid } = useMemo(() => {
        const { search } = location;
        let state;

        if (search) {
            state = parse(search);
            state.modalType = Number(state.modalType);
        }

        return {
            modalType: state.modalType || 1,
            dataSourceType: state?.dataSourceType || 'SYNC',
            uuid: state?.uuid
        };
    }, [location]);

    const [addEditModalData, setAddEditModalData] = useState({});
    const [current, setCurrent] = useState(0);
    const [systemList, setSystemList] = useState([]); // 字段列表
    const [documentTypeList, setDocumentTypeList] = useState([]); // 报文列表
    const [providerList, setProviderList] = useState([]); // 供应商列表
    const [etlList, setEtlList] = useState([]); // ETL列表
    const [featureSetList, setFeatureSetList] = useState([]); //指标集列表
    const [contractList, setContractList] = useState([]); // 合同列表

    useEffect(() => {
        initList();
    }, []);
    useEffect(() => {
        if (addEditModalData?.partnerId) {
            getContractList(addEditModalData.partnerId);
        }
    }, [addEditModalData, providerList]);

    useEffect(() => {
        if ([2, 3].includes(modalType)) {
            getDetail(uuid);
        }
    }, []);

    const back = () => {
        props.history.push('/handle/supplierManagement/dataServiceList');
    };

    // 根据ID获取数据服务
    const getDetail = (uuid) => {
        dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success && res.data) {
                const {
                    uuid,
                    partnerId,
                    dataType,
                    displayName,
                    confidence,
                    costLevel,
                    contractId,
                    chargeMethod,
                    cacheday,
                    cacheUnit,
                    cacheUnitNumber,
                    cacheOpen,
                    dbPassword,
                    dbUserName,
                    cacheInfrastructureType,
                    invokePolicy,
                    limitConfig,
                    retry,
                    timeout,
                    name,
                    methodType,
                    url,
                    contentType,
                    postEtlHandlerName,
                    preEtlHandlerName,
                    inputTemplate,
                    outputTemplate,
                    inputConfig,
                    outputConfig,
                    proxy,
                    proxyInfo,
                    indexPackageName,
                    documentTypeUuid,
                    pagination,
                    mockFlag,
                    mockType,
                    status,

                    // 异步接口相关数据
                    asyncPollTimeInterval,
                    asyncPollMaxCount,
                    asyncMethodType,
                    asyncUrl,
                    asyncContentType,
                    asyncProxy,
                    asyncProxyInfo,
                    asyncPreEtlHandlerName,
                    asyncPostEtlHandlerName,
                    asyncInputTemplate,
                    asyncOutputTemplate,
                    asyncInputConfig,
                    asyncOutputConfig
                } = res.data;
                const { globalStore } = props;
                const { allMap } = globalStore;

                let copyLimitConfig = limitConfig ? JSON.parse(limitConfig) : [];
                if (copyLimitConfig.length === 0) {
                    copyLimitConfig = [{ type: null, limit: null }];
                }

                let copyInputConfig = inputConfig ? JSON.parse(inputConfig) : [];
                copyInputConfig.forEach((item, index) => {
                    item.uuid = index;
                });
                let copyAsyncInputConfig = asyncInputConfig ? JSON.parse(asyncInputConfig) : [];
                copyAsyncInputConfig.forEach((item, index) => {
                    item.uuid = index;
                });
                let copyOutputConfig = outputConfig ? JSON.parse(outputConfig) : [];
                copyOutputConfig.forEach((item, index) => {
                    item.uuid = index;
                });
                let copyAsyncOutputConfig = asyncOutputConfig ? JSON.parse(asyncOutputConfig) : [];
                copyAsyncOutputConfig.forEach((item, index) => {
                    item.uuid = index;
                });

                setAddEditModalData({
                    uuid,
                    partnerId, // 供应商名称
                    dataType, // 数据类型
                    displayName, // 三方服务接口名称
                    name, // 三方数据标识
                    confidence, // 置信度
                    costLevel, // 成本等级
                    contractId, // 合同
                    chargeMethod, // 计费类型
                    // cacheday, // 数据缓存期（天）
                    cacheUnit, // 数据缓存期（单位）
                    cacheUnitNumber, // 数据缓存期（数量）
                    cacheInfrastructureType,
                    dbPassword, // 数据库密码
                    dbUserName, // 数据库用户名
                    retry, // 数据重试次数
                    timeout, // 数据超时时间（ms）
                    cacheOpen, // 查询方式（old） 1缓存/0实时查询
                    invokePolicy, // 查询方式
                    limitConfig: copyLimitConfig,
                    methodType, // 接口类型（协议）
                    url, // url地址
                    contentType, // 调用方式（Method）
                    postEtlHandlerName, // 后置ETL处理器
                    preEtlHandlerName, // 前置ETL处理器
                    inputTemplate, // 输入处理器模板
                    outputTemplate, // 输出处理器模板
                    pagination, // 是否分页
                    inputConfig: copyInputConfig, // 服务入参
                    outputConfig: copyOutputConfig, // 服务出参
                    mockFlag, // mock 开启状态
                    mockType, // mock 接口调用类型
                    status, // 上下线状态
                    proxys: {
                        proxy: proxy ? JSON.stringify(proxy) : '0',
                        proxyInfo: proxyInfo ? proxyInfo : allMap ? allMap.proxyInfo : null
                    },
                    indexPackageName: indexPackageName ? indexPackageName?.split(',') : [],
                    documentTypeUuid,

                    // 异步接口相关数据
                    asyncPollTimeInterval, // 轮询时间间隔
                    asyncPollMaxCount, // 最大轮询次数
                    asyncMethodType, // async 接口类型（协议）
                    asyncUrl, // async url地址
                    asyncContentType, // async 调用方式（Method）
                    asyncProxys: {
                        asyncProxy: asyncProxy ? JSON.stringify(asyncProxy) : '0', // async 是否使用代理
                        asyncProxyInfo
                    },
                    asyncPreEtlHandlerName, // async 前置ETL处理器
                    asyncPostEtlHandlerName, // async 后置ETL处理器
                    asyncInputTemplate, // async 输入处理器模板
                    asyncOutputTemplate, // async 输出处理器模板
                    asyncInputConfig: copyAsyncInputConfig, // async 服务入参
                    asyncOutputConfig: copyAsyncOutputConfig // async 服务出参
                });
            } else {
                message.error(res.message);
            }
        });
    };

    const initList = async () => {
        systemFieldsAPI.getListAll().then((res) => {
            if (res.success) {
                setSystemList(res.data);
            }
        });
        captainAPI.getDocumentTypeList().then((res) => {
            if (res?.data) {
                setDocumentTypeList(res.data);
            }
        });

        captainAPI.getIndexPackage().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setFeatureSetList(res.data);
            }
        });

        supplierListAPI.getList().then((res) => {
            if (res.success) {
                setProviderList(res?.data?.contents || []);
            }
        });
        etlAPI.getEtlList().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                setEtlList(res.data);
            } else {
                message.error(res.message);
            }
        });
    };
    const getContractList = (partnerId) => {
        const findObj = providerList.find((item) => item.uuid === partnerId);
        if (!findObj) return;
        contractListAPI.getList({ providerUuid: findObj?.uuid }).then((res) => {
            if (res.success) {
                setContractList(res?.data?.contents);
            }
        });
    };

    const valueTypeMap = {
        1: I18N.addmodify.serviceinput.ziFuXing,
        2: I18N.addmodify.serviceinput.zhengXing,
        3: I18N.addmodify.serviceinput.xiaoShuXing,
        4: I18N.addmodify.serviceinput.riQiXing,
        5: I18N.addmodify.serviceinput.buErXing
    };
    const confidenceMap = {
        1: '高',
        2: '中',
        3: '低'
    };
    const callMethodMap = {
        0: I18N.formlist.step1.benDiHuanCunYou,
        1: I18N.formlist.step1.zhiJieJieKou,
        2: I18N.formlist.step1.chuanCanYouXian
    };

    const isSync = dataSourceType === 'SYNC';

    let disabled = modalType === 3;
    // 切换详情和模拟调用
    const handleTabChange = (key) => {
        console.log(key);
    };

    const getIndexPackageName = (indexPackageName) => {
        const indexName = indexPackageName
            ?.map((item) => featureSetList.find((i) => i.name === item)?.displayName)
            ?.filter(Boolean)
            .join(',');
        return indexName;
    };

    const inputColumns = [
        {
            title: I18N.addmodify.serviceinput.xiTongZiDuan,
            dataIndex: 'field',
            width: 160,
            render: (text, record, index) => {
                const sysObj = systemList?.find((item) => item?.name === text);
                return sysObj ? <Ellipsis widthLimit={150}>{`${sysObj?.displayName}【${sysObj?.name}】`}</Ellipsis> : '- -';
            }
        },
        {
            title: (
                <Fragment>
                    {/* 映射 */}
                    {I18N.addmodify.serviceinput.yingShe}
                    <Tooltip title={I18N.addmodify.serviceinput.baXiTongZiDuan}>
                        <Icon style={{ marginLeft: 4 }} type="question-circle" />
                    </Tooltip>
                </Fragment>
            ),
            dataIndex: 'diraction',
            width: 120,
            align: 'center',
            render: () => {
                return <Icon type="arrow-right" />;
            }
        },
        {
            title: I18N.addmodify.serviceinput.canShuBiaoZhi, // 参数标识
            dataIndex: 'serviceParam',
            key: 'serviceParam',
            width: 100
        },
        {
            title: I18N.addmodify.serviceinput.canShuLeiXing, // 参数类型
            dataIndex: 'type',
            key: 'type',
            width: 100,
            render: (text, record, index) => {
                return text ? (text === 'variable' ? I18N.addmodify.serviceinput.bianLiang : I18N.addmodify.serviceinput.zhiDing) : '- -';
            }
        },
        {
            title: I18N.addmodify.serviceinput.zhiLeiXing, // 值类型
            dataIndex: 'value',
            key: 'value',
            width: 100,
            render: (text, record, index) => {
                if (record.type === 'constant') {
                    return '- -';
                }
                return text ? valueTypeMap[text] : '- -';
            }
        },
        {
            title: '是否必填', // 是否必填
            dataIndex: 'mustInput',
            key: 'mustInput',
            width: 60,
            render: (text, record) => {
                return record?.type === 'variable' ? (text ? '是' : '否') : '--';
            }
        },
        {
            title: 'HTTP',
            dataIndex: 'sendSpace',
            key: 'sendSpace',
            width: 100,
            render: (text, record, index) => {
                if (addEditModalData.methodType === 'socket') {
                    return 'body';
                }
                return text || '- -';
            }
        }
    ];

    const outputColumns = [
        {
            title: (
                <Fragment>
                    {/* 系统字段 */}
                    {I18N.addmodify.serviceoutput.xiTongZiDuan}
                    <Tooltip title={I18N.addmodify.serviceoutput.hePingTaiTiGong}>
                        <Icon style={{ marginLeft: 4 }} type="question-circle" />
                    </Tooltip>
                </Fragment>
            ),
            dataIndex: 'field',
            width: 160,
            render: (text, record, index) => {
                const sysObj = systemList?.find((item) => item?.name === text);
                return sysObj ? <Ellipsis widthLimit={150}>{`${sysObj.displayName}【${sysObj.name}】`}</Ellipsis> : '- -';
            }
        },
        {
            title: (
                <Fragment>
                    {/* 映射 */}
                    {I18N.addmodify.serviceoutput.yingShe}
                    <Tooltip title={I18N.addmodify.serviceoutput.baFuWuChuCan}>
                        <Icon style={{ marginLeft: 4 }} type="question-circle" />
                    </Tooltip>
                </Fragment>
            ),
            dataIndex: 'diraction',
            width: 100,
            align: 'center',
            render: () => {
                return <Icon type="arrow-left" />;
            }
        },
        {
            title: I18N.addmodify.serviceoutput.canShuBiaoZhi, // 参数标识
            dataIndex: 'serviceParam',
            key: 'serviceParam',
            width: 160
        },
        {
            title: I18N.addmodify.serviceoutput.canShuLeiXing, // 参数类型
            dataIndex: 'type',
            key: 'type',
            width: 80,
            render: (text, record, index) => {
                return text ? (text === 'variable' ? I18N.addmodify.serviceoutput.bianLiang : I18N.addmodify.serviceoutput.dingZhi) : '- -';
            }
        },
        {
            title: (
                <>
                    {I18N.addmodify.serviceoutput.chaDeZiDuan}
                    <Tooltip title={I18N.addmodify.serviceoutput.ruGuoJieKouWei}>
                        <Icon style={{ marginLeft: 4 }} type="question-circle" />
                    </Tooltip>
                </>
            ), // 查得字段
            dataIndex: 'includeCheck',
            key: 'includeCheck',
            width: 190,
            render: (text, record) => {
                return text ? (text === 1 ? I18N.addmodify.serviceoutput.feiKong : I18N.addmodify.serviceoutput.quZhi) : '- -';
            }
        },
        {
            title: () => {
                return (
                    <>
                        <span>{I18N.addmodify.serviceoutput.morenzhi}</span>
                        <Tooltip title={I18N.addmodify.serviceoutput.morenzhiTitle}>
                            <Icon style={{ marginLeft: 4 }} type="question-circle" />
                        </Tooltip>
                    </>
                );
            },
            dataIndex: 'defaultValue',
            width: 140
        },
        {
            title: () => {
                return (
                    <>
                        <span>{I18N.addmodify.serviceoutput.geShiJiaoYan}</span>
                        <Tooltip
                            title={
                                <>
                                    <div>{I18N.addmodify.serviceoutput.shuRuZhengZeDui}</div>
                                    <div>{I18N.addmodify.serviceoutput.shiLi}</div>
                                    <div>
                                        {I18N.addmodify.serviceoutput.shenFenZhengHaoD}
                                        {5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d
                                        {3}[0-9Xx]$
                                    </div>
                                    <div>
                                        {' '}
                                        {I18N.addmodify.serviceoutput.shouJiHaoD}
                                        {9}${' '}
                                    </div>
                                    <div>{I18N.addmodify.serviceoutput.youXiangHaoAZ}</div>
                                </>
                            }>
                            <Icon style={{ marginLeft: 4 }} type="question-circle" />
                        </Tooltip>
                    </>
                );
            },
            dataIndex: 'formatCheck',
            width: 140,
            render: (text, record, index) => {
                //只对字符类型的字段进行正则校验，其他显示 - -
                return record?.dataType === 1 ? text : '- -';
            }
        }
    ];

    const limitColumns = [
        {
            title: '监控维度',
            dataIndex: 'type',
            width: 200,
            render: (text, record) => {
                return text ? text : '- -';
            }
        },
        {
            title: '流量阈值',
            dataIndex: 'limit',
            width: 200,
            render: (text, record) => {
                return text ? text : '- -';
            }
        }
    ];
    const cacheOptions = [
        {
            label: I18N.addmodify.cachetime.dangtian,
            value: 'SAME_DAY'
        },
        {
            label: I18N.addmodify.cachetime.shi,
            value: 'HOURS',
            placeholder: I18N.addmodify.cachetime.qingshuruxiaoshi
        },
        {
            label: I18N.addmodify.cachetime.tian,
            value: 'DAYS',
            placeholder: I18N.addmodify.cachetime.qingshurutianshu
        }
    ];

    // 编辑按钮点击事件处理函数
    const handleEdit = () => {
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 2
            }
        });
        if (dataSourceType === 'SYNC') {
            let path = '/handle/supplierManagement/dataServiceList/addModify/sync';
            history.push(`${path}?modalType=2&uuid=${uuid}&dataSourceType=${dataSourceType}`);
        } else {
            let path = '/handle/supplierManagement/dataServiceList/addModify';
            history.push(`${path}?modalType=2&uuid=${uuid}&dataSourceType=${dataSourceType}`);
        }
    };

    return (
        <div className="g-dataservice-detail">
            <div className="page-global-header">
                <div className="left-info">
                    <span className="u-back" onClick={back}>
                        <Icon type="left" />
                        {I18N.addmodify.index.fanHui}
                    </span>
                    <span style={{ color: '#8b919e' }} className="u-title">
                        {'详情'}
                    </span>
                </div>
            </div>
            <div className="page-global-body">
                <div className="service-header">
                    <div className="service-header-iconInfo">
                        <div className="service-icon">
                            <span>{addEditModalData?.displayName?.slice(0, 1) || '- -'}</span>
                        </div>
                        <div className="service-info">
                            <div className="service-title-row">
                                <div className="service-title-container">
                                    <h3 className="service-title">
                                        <Ellipsis widthLimit={300}>{addEditModalData?.displayName || '- -'}</Ellipsis>{' '}
                                    </h3>
                                    <span className="service-copy-icon">
                                        <Tooltip placement="top" title={addEditModalData?.name}>
                                            <Icon type="id" style={{ fontSize: 20, color: '#8B919E' }} />
                                        </Tooltip>
                                    </span>
                                </div>
                                <Badge
                                    style={{ marginLeft: 16 }}
                                    color={addEditModalData?.status === 1 ? 'green' : 'red'}
                                    text={addEditModalData?.status === 1 ? '上线' : '下线'}
                                />
                            </div>
                            <div className="service-tags">
                                {addEditModalData?.dataType && (
                                    <Tag border={false} size="small">
                                        {allMap?.serviceTypeList?.find((item) => item?.dataType === addEditModalData.dataType)?.name}
                                    </Tag>
                                )}
                                {dataSourceType === 'ASYNC' && (
                                    <Tag border={false} size="small">
                                        异步
                                    </Tag>
                                )}
                            </div>
                        </div>
                    </div>
                    <Button type="primary" icon="form" onClick={handleEdit}>
                        编辑
                    </Button>
                </div>
                <div className="service-content">
                    <Tabs defaultActiveKey="detail" onChange={handleTabChange}>
                        <TabPane tab="详情" key="detail">
                            <div id="service-content-basicInfo" className="service-content-basicInfo">
                                <Title title="基础配置" />
                                <div className="service-content-detail">
                                    <Descriptions ellipsis={true}>
                                        <Descriptions.Item label="Url地址">{addEditModalData?.url || '- -'}</Descriptions.Item>
                                        <Descriptions.Item label="调用方式">{addEditModalData?.contentType || '- -'}</Descriptions.Item>
                                        <Descriptions.Item label="协议">{addEditModalData?.methodType || '- -'}</Descriptions.Item>
                                        <Descriptions.Item label="报文">
                                            {documentTypeList?.find((item) => item?.uuid === addEditModalData?.documentTypeUuid)
                                                ?.displayName || '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="指标集">
                                            {getIndexPackageName(addEditModalData?.indexPackageName) || '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="超时时长">{addEditModalData?.timeout || '- -'}</Descriptions.Item>
                                        <Descriptions.Item label="重试次数">{addEditModalData?.retry || '- -'}</Descriptions.Item>
                                        <Descriptions.Item label="分页接口">
                                            <Badge
                                                className="service-content-detail-badge"
                                                color={addEditModalData?.pagination ? '#07C790' : '#8B919E'}
                                                text={addEditModalData?.pagination ? '已开启' : '未开启'}
                                            />
                                        </Descriptions.Item>
                                        <Descriptions.Item label="使用代理">
                                            <Badge
                                                className="service-content-detail-badge"
                                                color={addEditModalData?.asyncProxys?.asyncProxy === '0' ? '#07C790' : '#8B919E'}
                                                text={addEditModalData?.asyncProxys?.asyncProxy === '0' ? '已使用' : '未使用'}
                                            />
                                        </Descriptions.Item>
                                    </Descriptions>
                                </div>
                            </div>
                            <div id="service-content-contractBilling" className="service-content-contractBilling">
                                <Title title="合同计费与出入参" />
                                <div className="service-content-detail">
                                    <h4>合同计费</h4>
                                    <Descriptions ellipsis={true}>
                                        <Descriptions.Item label="数据类型">
                                            {allMap?.serviceTypeList?.find((item) => item?.dataType === addEditModalData.dataType)?.name ||
                                                '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="合作方名称">
                                            {providerList?.find((item) => item?.uuid === addEditModalData?.partnerId)?.displayName || '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="合同">
                                            {contractList?.find((item) => item?.uuid === addEditModalData?.contractId)?.name || '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item
                                            label={
                                                <span style={{ verticalAlign: 'middle' }}>
                                                    <span>{I18N.formlist.step1.zhiXinDu}</span>
                                                    <Tooltip title={I18N.formlist.step1.jieHeJingYanDui}>
                                                        <Icon type="question-circle" />
                                                    </Tooltip>
                                                </span>
                                            }>
                                            {addEditModalData?.confidence ? confidenceMap[addEditModalData?.confidence] : '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item
                                            label={
                                                <span style={{ verticalAlign: 'middle' }}>
                                                    <span>{I18N.formlist.step1.chengBenDengJi}</span>
                                                    <Tooltip title={I18N.formlist.step1.genJuShuJuYuan}>
                                                        <Icon type="question-circle" />
                                                    </Tooltip>
                                                </span>
                                            }>
                                            {addEditModalData?.costLevel ? confidenceMap[addEditModalData?.costLevel] : '- -'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="计费类型">
                                            {chargeMethodList?.find((item) => item.code === addEditModalData?.chargeMethod)?.name || '- -'}
                                        </Descriptions.Item>
                                    </Descriptions>
                                </div>
                                <div className="service-content-detail">
                                    <h4>服务入参</h4>
                                    <Table bordered columns={inputColumns} dataSource={addEditModalData?.inputConfig || []} />
                                </div>
                                <div className="service-content-detail">
                                    <h4>服务出参</h4>
                                    <Descriptions column={1} ellipsis={true}>
                                        <Descriptions.Item label="后置ETL处理器">
                                            {addEditModalData?.postEtlHandlerName || '- -'}
                                        </Descriptions.Item>
                                    </Descriptions>
                                    <div className="service-content-detail-output">
                                        <span>输出处理器模版</span>
                                        <div>{addEditModalData?.outputTemplate || '- -'}</div>
                                    </div>
                                    <Table bordered columns={outputColumns} dataSource={addEditModalData?.outputConfig || []} />
                                </div>
                            </div>
                            <div id="service-content-cacheSettings" className="service-content-cacheSettings">
                                <Title title="缓存设置" />
                                <div className="service-content-detail">
                                    <Descriptions ellipsis={true}>
                                        <Descriptions.Item label="调用方式">
                                            {callMethodMap[addEditModalData?.invokePolicy]}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="缓存设置">
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <span>
                                                    {addEditModalData?.cacheUnitNumber
                                                        ? `${addEditModalData.cacheUnitNumber} ${
                                                              cacheOptions?.find((item) => item.value === addEditModalData?.cacheUnit)
                                                                  ?.label || ''
                                                          }`.trim()
                                                        : '- -'}
                                                </span>
                                                {addEditModalData?.cacheInfrastructureType === 'elasticsearch' && (
                                                    <Tag style={{ marginLeft: 8 }} border={false} size="small" color="#E6F4FF">
                                                        <span style={{ color: '#126BFB' }}>{'持久化'}</span>
                                                    </Tag>
                                                )}
                                            </div>
                                        </Descriptions.Item>
                                    </Descriptions>
                                </div>
                            </div>
                            <div id="service-content-flowControl" className="service-content-flowControl">
                                <Title title="流量控制" />
                                <div className="service-content-flowControl-content">任一监控维度流量达到上限均会触发流量控制</div>
                                <FlowLimitConfigTable value={addEditModalData?.limitConfig || []} modalType={modalType} />
                            </div>
                        </TabPane>
                        <TabPane tab="模拟调用" key="mock">
                            <MockConfig documentTypeList={documentTypeList} />
                        </TabPane>
                    </Tabs>
                </div>
            </div>
        </div>
    );
});

export default PageContainer(Detail);
